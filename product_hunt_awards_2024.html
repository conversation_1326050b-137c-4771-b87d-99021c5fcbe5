<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Hunt Golden Kitty Awards 2024</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            max-width: 1400px;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
            border-radius: 2px;
        }

        .title {
            font-size: 48px;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 16px;
            letter-spacing: -1px;
        }

        .subtitle {
            font-size: 24px;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .year {
            font-size: 18px;
            color: #94a3b8;
            font-weight: 400;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .category-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            padding: 24px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .category-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
            border-color: #cbd5e1;
        }

        .category-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .category-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .winners-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .winner-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: white;
            border-radius: 12px;
            border: 1px solid #f1f5f9;
            transition: all 0.2s ease;
            position: relative;
        }

        .winner-item:hover {
            background: #f8fafc;
            border-color: #e2e8f0;
        }

        .winner-rank {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            flex-shrink: 0;
        }

        .winner-rank.first {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #92400e;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .winner-rank.second {
            background: linear-gradient(135deg, #c0c0c0, #e5e7eb);
            color: #374151;
        }

        .winner-rank.third {
            background: linear-gradient(135deg, #cd7f32, #d97706);
            color: white;
        }

        .winner-info {
            flex: 1;
        }

        .winner-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .winner-description {
            font-size: 13px;
            color: #64748b;
            line-height: 1.4;
        }

        .special-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            border-radius: 20px;
            padding: 32px;
            margin: 40px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .special-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .special-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            position: relative;
            z-index: 1;
        }

        .special-winner {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            position: relative;
            z-index: 1;
        }

        .special-winner-name {
            font-size: 24px;
            font-weight: 700;
        }

        .special-winner-desc {
            font-size: 16px;
            opacity: 0.9;
            margin-top: 8px;
        }

        @media (max-width: 768px) {
            .categories-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .title {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 20px;
            }
            
            .container {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Golden Kitty Awards</h1>
            <p class="subtitle">Product Hunt 年度最佳产品</p>
            <p class="year">2024年度获奖者</p>
        </div>

        <!-- Product of the Year -->
        <div class="special-section">
            <h2 class="special-title">🏆 年度产品大奖</h2>
            <div class="special-winner">
                <div>
                    <div class="special-winner-name">Cursor</div>
                    <div class="special-winner-desc">AI代码编辑器 - 重新定义开发体验</div>
                </div>
            </div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-top: 20px; position: relative; z-index: 1;">
                <div style="text-align: center;">
                    <div style="font-size: 14px; opacity: 0.8;">亚军</div>
                    <div style="font-weight: 600;">Supabase</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 14px; opacity: 0.8;">季军</div>
                    <div style="font-weight: 600;">OpenAI o1</div>
                </div>
            </div>
        </div>

        <div class="categories-grid">
            <!-- Developer Tools -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">💻</span>
                    开发者工具
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Supabase</div>
                            <div class="winner-description">Postgres开发者平台，现已正式发布</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Cursor</div>
                            <div class="winner-description">AI代码编辑器</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">bolt.new</div>
                            <div class="winner-description">提示、运行、编辑和部署全栈Web应用</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Design Tools -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">🎨</span>
                    设计工具
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Figma AI</div>
                            <div class="winner-description">释放你的创造力</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Mobbin 2.0</div>
                            <div class="winner-description">发现真实世界的设计灵感</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Layers</div>
                            <div class="winner-description">设计师的家园</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Model -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">🤖</span>
                    AI模型
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Claude 3</div>
                            <div class="winner-description">为工作而生的下一代AI，安全准确</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">GPT-4o</div>
                            <div class="winner-description">OpenAI的新旗舰模型</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Llama 3.1-405B</div>
                            <div class="winner-description">可与GPT-4o/Claude-3.5媲美的开源模型</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI for Video -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">🎬</span>
                    AI视频
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Sora by OpenAI</div>
                            <div class="winner-description">使用文本提示创建分钟级视频</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Runway: Gen-3 Alpha</div>
                            <div class="winner-description">Runway的新视频生成基础模型</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Google Veo 2</div>
                            <div class="winner-description">Google最先进的视频生成模型</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Code -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">⚡</span>
                    无代码工具
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Notion Sites</div>
                            <div class="winner-description">几分钟内启动美观网站</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">HeyForm 3.0</div>
                            <div class="winner-description">开源表单构建器，为小企业成功而生</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Wegic</div>
                            <div class="winner-description">首个AI网页设计师和开发者助手</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile App -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">📱</span>
                    移动应用
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Remy AI</div>
                            <div class="winner-description">任何人都能睡得更好，恢复更佳</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Earth.fm App</div>
                            <div class="winner-description">900+自然音景直接在手机上</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Arc Search</div>
                            <div class="winner-description">快速无广告网页浏览</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI for Audio -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">🎵</span>
                    AI音频
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">ElevenLabs</div>
                            <div class="winner-description">顶级AI文本转语音 | 语音克隆</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Voicenotes</div>
                            <div class="winner-description">真正智能的AI笔记工具</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Vapi</div>
                            <div class="winner-description">互联网语音AI基础设施</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Finance Tech -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">💰</span>
                    金融科技
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Mercury Personal</div>
                            <div class="winner-description">Mercury上的强大个人和联名账户</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Central</div>
                            <div class="winner-description">初创公司的薪资、福利和政府事务</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Gilion</div>
                            <div class="winner-description">预测你的未来，资助你的未来</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Personal Productivity -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">⚡</span>
                    个人效率
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Raycast Notes</div>
                            <div class="winner-description">快速、轻量、无摩擦的笔记工具</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Notion Calendar</div>
                            <div class="winner-description">为工作和生活精心设计</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Findr</div>
                            <div class="winner-description">一次搜索所有应用</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Open Source -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">🔓</span>
                    开源项目
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">postgres.new</div>
                            <div class="winner-description">带AI辅助的浏览器内Postgres沙盒</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Meta Llama 3</div>
                            <div class="winner-description">迄今最强大的开放可用LLM</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Helicone AI</div>
                            <div class="winner-description">开发者的开源LLM可观测性</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Hardware -->
            <div class="category-card">
                <h3 class="category-title">
                    <span class="category-icon">🔧</span>
                    AI硬件
                </h3>
                <div class="winners-list">
                    <div class="winner-item">
                        <div class="winner-rank first">1</div>
                        <div class="winner-info">
                            <div class="winner-name">Oura Ring 4</div>
                            <div class="winner-description">更纤薄设计，更高精度，更多尺寸</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank second">2</div>
                        <div class="winner-info">
                            <div class="winner-name">Limitless</div>
                            <div class="winner-description">基于你所见、所说、所听的个性化AI</div>
                        </div>
                    </div>
                    <div class="winner-item">
                        <div class="winner-rank third">3</div>
                        <div class="winner-info">
                            <div class="winner-name">Friend</div>
                            <div class="winner-description">可穿戴伴侣</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Maker of the Year -->
        <div class="special-section" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); margin-top: 40px;">
            <h2 class="special-title">👨‍💻 年度制造者</h2>
            <div class="special-winner">
                <div>
                    <div class="special-winner-name">Pascal Pixel</div>
                    <div class="special-winner-desc">@pascalpixel - Horse Browser创始人，Product Hunt社区活跃贡献者</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
