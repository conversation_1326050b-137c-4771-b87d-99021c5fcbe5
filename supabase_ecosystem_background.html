<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术变革下的生态研究 - Supabase案例分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 40px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 50px;
            max-width: 1200px;
            width: 100%;
            height: 750px;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
        }
        
        .title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 18px;
            color: #64748b;
            font-weight: 400;
        }
        
        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr auto;
            gap: 30px;
            flex: 1;
            position: relative;
        }
        
        .section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #2563eb;
            position: relative;
        }

        .card-supabase {
            grid-column: 1;
            grid-row: 1;
        }

        .card-impact {
            grid-column: 2;
            grid-row: 1;
        }

        .card-insight {
            grid-column: 1 / -1;
            grid-row: 2;
            background: #1e293b;
            color: white;
            border-left: 4px solid #60a5fa;
        }

        .arrow {
            position: absolute;
            font-size: 24px;
            color: #2563eb;
            font-weight: bold;
        }

        .arrow-right {
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
        }

        .arrow-down-left {
            bottom: -25px;
            left: 30%;
            transform: translateX(-50%);
        }

        .arrow-down-right {
            bottom: -25px;
            right: 30%;
            transform: translateX(50%);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #2563eb;
            border-radius: 50%;
            margin-right: 12px;
        }
        
        .fact-item {
            margin-bottom: 15px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #10b981;
        }
        
        .fact-label {
            font-weight: 600;
            color: #059669;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .fact-content {
            color: #374151;
            font-size: 15px;
            line-height: 1.5;
        }
        
        .trend-item {
            margin-bottom: 15px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #f59e0b;
        }
        
        .trend-label {
            font-weight: 600;
            color: #d97706;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .insight-item {
            margin-bottom: 15px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #8b5cf6;
        }
        
        .insight-label {
            font-weight: 600;
            color: #7c3aed;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .card-insight .insight-label {
            color: #60a5fa;
        }

        .tco-note {
            font-size: 12px;
            color: #6b7280;
            font-style: italic;
            margin-top: 5px;
            padding: 8px;
            background: #f9fafb;
            border-radius: 4px;
            border-left: 2px solid #d97706;
        }
        
        .highlight {
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        

    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">技术变革对生态研究的启示</h1>
            <p class="subtitle">从Supabase带来的变化，延伸想到的一些可能性……</p>
        </div>
        
        <div class="content">
            <!-- 卡片一：Supabase -->
            <div class="section card-supabase">
                <h2 class="section-title">Supabase</h2>

                <div class="fact-item">
                    <div class="fact-label">市场地位</div>
                    <div class="fact-content">2024年完成<span class="highlight">8000万美元C轮融资</span>，成为开源Firebase替代方案领导者</div>
                </div>

                <div class="fact-item">
                    <div class="fact-label">成本优势</div>
                    <div class="fact-content">相比AWS RDS提供<span class="highlight">60-80%成本节省</span>，免费层级支持5万行数据</div>
                </div>

                <div class="fact-item">
                    <div class="fact-label">开发效率</div>
                    <div class="fact-content">一体化平台，开发时间相比传统云服务<span class="highlight">减少70%</span></div>
                </div>

                <div class="fact-item">
                    <div class="fact-label">目标用户</div>
                    <div class="fact-content">主要服务<span class="highlight">个人开发者和中小企业</span>，GitHub获得65k+ stars</div>
                </div>

                <div class="arrow arrow-down-left">↓</div>
            </div>

            <!-- 卡片二：类Supabase新势力带来的影响 -->
            <div class="section card-impact">
                <h2 class="section-title">类Supabase新势力带来的影响</h2>

                <div class="trend-item">
                    <div class="trend-label">TCO优化趋势</div>
                    <div class="fact-content">企业更关注<span class="highlight">总体拥有成本</span>，传统云厂商高昂费用成为痛点</div>
                    <div class="tco-note">
                        <strong>TCO (Total Cost of Ownership)</strong>：包括软件许可、硬件、实施、维护、培训等全生命周期成本
                    </div>
                </div>

                <div class="trend-item">
                    <div class="trend-label">去中心化加速</div>
                    <div class="fact-content">开源生态<span class="highlight">挑战传统云巨头垄断</span>，开发者寻求更透明的解决方案</div>
                </div>

                <div class="trend-item">
                    <div class="trend-label">开发体验革命</div>
                    <div class="fact-content">一体化平台成为主流，<span class="highlight">简化技术栈</span>比功能丰富度更重要</div>
                </div>

                <div class="trend-item">
                    <div class="trend-label">避免厂商锁定</div>
                    <div class="fact-content">企业重视<span class="highlight">技术独立性</span>，开源方案提供更好的迁移能力</div>
                </div>

                <div class="arrow arrow-down-right">↓</div>
            </div>

            <!-- 卡片三：对本项目的启示 -->
            <div class="section card-insight">
                <h2 class="section-title" style="color: #60a5fa;">对本项目的启示</h2>

                <div class="insight-item">
                    <div class="insight-label">重点关注群体</div>
                    <div class="fact-content" style="color: #e2e8f0;">针对<span class="highlight" style="background: #374151; color: #60a5fa;">中小企业及个人开发者</span>，他们是技术变革的敏感群体和早期采用者</div>
                </div>

                <div class="insight-item">
                    <div class="insight-label">态度与看法调研</div>
                    <div class="fact-content" style="color: #e2e8f0;">深入了解他们对<span class="highlight" style="background: #374151; color: #60a5fa;">新技术、新产品的态度与看法</span>，识别影响技术选择的关键因素</div>
                </div>

                <div class="insight-item">
                    <div class="insight-label">需求变化感知</div>
                    <div class="fact-content" style="color: #e2e8f0;">研究新技术新产品给他们带来的<span class="highlight" style="background: #374151; color: #60a5fa;">对生态的需求与感知变化</span></div>
                </div>

                <div class="insight-item">
                    <div class="insight-label">健康度评估维度</div>
                    <div class="fact-content" style="color: #e2e8f0;">建立<span class="highlight" style="background: #374151; color: #60a5fa;">替代风险预警机制</span>，监测开源替代方案对华为生态的潜在冲击</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
