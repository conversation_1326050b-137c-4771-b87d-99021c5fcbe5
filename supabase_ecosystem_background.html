<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术变革下的生态研究 - Supabase案例分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #fafafa;
            padding: 20px;
            margin: 0;
            overflow: hidden;
        }

        .container {
            background: white;
            border: 1px solid #e5e7eb;
            padding: 30px;
            max-width: 1400px;
            width: 100%;
            height: 100vh;
            max-height: 900px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }
        
        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 15px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .subtitle {
            font-size: 14px;
            color: #6b7280;
            font-weight: 400;
        }
        
        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 200px;
            gap: 20px;
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        
        .section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .card-supabase {
            grid-column: 1;
            grid-row: 1;
        }

        .card-impact {
            grid-column: 2;
            grid-row: 1;
        }

        .card-insight {
            grid-column: 1 / -1;
            grid-row: 2;
            background: #374151;
            color: white;
            border: 1px solid #4b5563;
        }

        .arrow {
            position: absolute;
            font-size: 18px;
            color: #9ca3af;
            font-weight: normal;
        }

        .arrow-down-left {
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
        }

        .arrow-down-right {
            bottom: -15px;
            right: 50%;
            transform: translateX(50%);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #d1d5db;
        }
        
        .fact-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .fact-label {
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            margin-bottom: 3px;
        }

        .fact-content {
            color: #6b7280;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .trend-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .trend-label {
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            margin-bottom: 3px;
        }
        
        .insight-item {
            margin-bottom: 8px;
            padding: 6px 0;
        }

        .insight-label {
            font-weight: 600;
            color: #d1d5db;
            font-size: 12px;
            margin-bottom: 3px;
        }

        .tco-note {
            font-size: 11px;
            color: #9ca3af;
            font-style: italic;
            margin-top: 4px;
            padding: 6px;
            background: #4b5563;
            border-left: 2px solid #6b7280;
        }
        
        .highlight {
            background: #f3f4f6;
            padding: 1px 4px;
            font-weight: 600;
            color: #111827;
        }
        

    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">技术变革对生态研究的启示</h1>
            <p class="subtitle">从Supabase带来的变化，延伸想到的一些可能性……</p>
        </div>
        
        <div class="content">
            <!-- 卡片一：Supabase -->
            <div class="section card-supabase">
                <h2 class="section-title">Supabase</h2>

                <div class="fact-item">
                    <div class="fact-label">市场地位</div>
                    <div class="fact-content">2024年完成<span class="highlight">8000万美元C轮融资</span>，开源Firebase替代方案领导者</div>
                </div>

                <div class="fact-item">
                    <div class="fact-label">成本优势</div>
                    <div class="fact-content">相比AWS RDS提供<span class="highlight">60-80%成本节省</span>，免费层级支持5万行数据</div>
                </div>

                <div class="fact-item">
                    <div class="fact-label">开发效率</div>
                    <div class="fact-content">一体化平台，开发时间相比传统云服务<span class="highlight">减少70%</span></div>
                </div>

                <div class="fact-item">
                    <div class="fact-label">目标用户</div>
                    <div class="fact-content">主要服务<span class="highlight">个人开发者和中小企业</span>，GitHub获得65k+ stars</div>
                </div>

                <div class="arrow arrow-down-left">↓</div>
            </div>

            <!-- 卡片二：类Supabase新势力带来的影响 -->
            <div class="section card-impact">
                <h2 class="section-title">类Supabase新势力带来的影响</h2>

                <div class="trend-item">
                    <div class="trend-label">TCO优化趋势</div>
                    <div class="fact-content">企业更关注<span class="highlight">总体拥有成本</span>，传统云厂商高昂费用成为痛点</div>
                    <div class="tco-note">
                        <strong>TCO (Total Cost of Ownership)</strong>：包括软件许可、硬件、实施、维护、培训等全生命周期成本
                    </div>
                </div>

                <div class="trend-item">
                    <div class="trend-label">去中心化加速</div>
                    <div class="fact-content">开源生态<span class="highlight">挑战传统云巨头垄断</span>，开发者寻求更透明解决方案</div>
                </div>

                <div class="trend-item">
                    <div class="trend-label">开发体验革命</div>
                    <div class="fact-content">一体化平台成为主流，<span class="highlight">简化技术栈</span>比功能丰富度更重要</div>
                </div>

                <div class="trend-item">
                    <div class="trend-label">避免厂商锁定</div>
                    <div class="fact-content">企业重视<span class="highlight">技术独立性</span>，开源方案提供更好迁移能力</div>
                </div>

                <div class="arrow arrow-down-right">↓</div>
            </div>

            <!-- 卡片三：对本项目的启示 -->
            <div class="section card-insight">
                <h2 class="section-title" style="color: #d1d5db; border-bottom: 1px solid #4b5563;">对本项目的启示</h2>

                <div class="insight-item">
                    <div class="fact-content" style="color: #e5e7eb; font-size: 14px; line-height: 1.6;">
                        针对<span class="highlight" style="background: #4b5563; color: #f9fafb; padding: 2px 6px;">中小企业及个人开发者</span>，特别关注他们对新技术、新产品的态度与看法，并了解（新技术新产品给他们带来的）对生态的需求与感知的变化
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
